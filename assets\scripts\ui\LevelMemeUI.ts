import { _decorator, CCInteger, Component, instantiate, JsonAsset, Node, Prefab, Vec3 } from 'cc';
import { MemeGame } from '../meme/MemeGame';
import { ColorConfigs } from '../game/board/ColorConfigs';
import { ColorType } from '../enums/Enums';
const { ccclass, property } = _decorator;

@ccclass('LevelMemeUI')
export class LevelMemeUI extends Component {
    @property(Node)
    content: Node = null!;

    @property(Prefab)
    memePrefab: Prefab = null!;

    @property(JsonAsset)
    levelMemeData: JsonAsset = null!;

    @property(CCInteger)
    level: number = 0;

    private _memePositions: Map<ColorType, Vec3> = new Map();

    protected onLoad(): void {

        this.content.destroyAllChildren();

        const levelData = this.levelMemeData.json;
        const level = levelData[this.level];

        const arrMeme = level.Meme;
        for (const meme of arrMeme) {
            const memeNode = instantiate(this.memePrefab);
            memeNode.setParent(this.content);
            memeNode.getComponent(MemeGame).setupView(ColorConfigs.Instance.getSpriteMemeFromColor(meme.Color), meme.Amount);
            this._memePositions.set(meme.Color, memeNode.worldPosition);
        }
    }

    public getPosMemeGem(color: ColorType): Vec3 {
        return this._memePositions.get(color);
    }

}


