import { _decorator, Camera, Component, Node, Prefab, Vec3, instantiate, Sprite, UITransform } from 'cc';
import { ColorType } from '../enums/Enums';
import { ColorConfigs } from '../game/board/ColorConfigs';
const { ccclass, property } = _decorator;

@ccclass('ContentMemeUI')
export class ContentMemeUI extends Component {

    @property(Camera)
    public camera: Camera = null!;

    @property(Prefab)
    public memePrefab: Prefab = null!;

    private static _instance: ContentMemeUI | null = null;

    public static get Instance(): ContentMemeUI {
        if (!this._instance) {
            console.error("GameManager instance is not yet available.");
        }
        return this._instance!;
    }

    onLoad() {
        ContentMemeUI._instance = this;
    }

    public createMeme(pos: Vec3, color: ColorType) {
        const meme = instantiate(this.memePrefab);
        meme.setParent(this.node);

        const spr = ColorConfigs.Instance.getSpriteMemeFromColor(color);
        meme.getComponent(Sprite).spriteFrame = spr;

        // <PERSON>yển đổi từ world position 3D sang screen coordinates
        const screenPos = new Vec3();
        this.camera.worldToScreen(pos, screenPos);

        // Chuyển đổi từ screen coordinates sang UI coordinates (local position trên canvas)
        const uiTransform = this.node.getComponent(UITransform);
        if (uiTransform) {
            const canvasPos = new Vec3();
            // Chuyển đổi từ screen space sang node space của canvas
            uiTransform.convertToNodeSpaceAR(screenPos, canvasPos);
            meme.setPosition(canvasPos);
        } else {
            // Fallback: sử dụng screen position trực tiếp (có thể cần điều chỉnh)
            meme.setPosition(screenPos.x, screenPos.y, 0);
        }
    }
}




