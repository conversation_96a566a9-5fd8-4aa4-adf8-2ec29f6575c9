import { _decorator, Camera, Component, Node, Prefab, Vec3, instantiate, Sprite } from 'cc';
import { ColorType } from '../enums/Enums';
import { ColorConfigs } from '../game/board/ColorConfigs';
const { ccclass, property } = _decorator;

@ccclass('ContentMemeUI')
export class ContentMemeUI extends Component {

    @property(Camera)
    public camera: Camera = null!;

    @property(Prefab)
    public memePrefab: Prefab = null!;

    private static _instance: ContentMemeUI | null = null;

    public static get Instance(): ContentMemeUI {
        if (!this._instance) {
            console.error("GameManager instance is not yet available.");
        }
        return this._instance!;
    }

    onLoad() {
        ContentMemeUI._instance = this;
    }

    public createMeme(pos: Vec3, color: ColorType) {

        const meme = instantiate(this.memePrefab);
        meme.setParent(this.node);

        const spr = ColorConfigs.Instance.getSpriteMemeFromColor(color);
        meme.getComponent(Sprite).spriteFrame = spr;

        const outPos = new Vec3();
        this.camera.worldToScreen(pos, outPos);
        meme.setPosition(outPos);
    }
}




