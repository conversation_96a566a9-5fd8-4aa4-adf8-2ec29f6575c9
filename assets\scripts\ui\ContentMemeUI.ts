import { _decorator, Camera, Component, Node, Prefab, Vec3, instantiate, Sprite, UITransform, tween } from 'cc';
import { ColorType } from '../enums/Enums';
import { ColorConfigs } from '../game/board/ColorConfigs';
import { LevelMemeUI } from './LevelMemeUI';
const { ccclass, property } = _decorator;

@ccclass('ContentMemeUI')
export class ContentMemeUI extends Component {

    @property(Camera)
    public camera: Camera = null!;

    @property(Prefab)
    public memePrefab: Prefab = null!;

    @property(LevelMemeUI)
    levelMemeUI: LevelMemeUI = null!;

    private static _instance: ContentMemeUI | null = null;

    public static get Instance(): ContentMemeUI {
        if (!this._instance) {
            console.error("GameManager instance is not yet available.");
        }
        return this._instance!;
    }

    onLoad() {
        ContentMemeUI._instance = this;
    }

    public createMeme(pos: Vec3, color: ColorType) {
        const meme = instantiate(this.memePrefab);
        meme.setParent(this.node);
        meme.setScale(Vec3.ZERO);

        tween(meme)
            .to(0.5, { scale: Vec3.ONE }, { easing: 'backOut' })
            .start();

        const spr = ColorConfigs.Instance.getSpriteMemeFromColor(color);
        meme.getComponent(Sprite).spriteFrame = spr;

        // Thử cách khác: Sử dụng screenToWorld và convertToNodeSpaceAR
        const screenPos = new Vec3();
        this.camera.convertToUINode(pos, this.node, screenPos);

        meme.setPosition(screenPos);

        const


    }
}




