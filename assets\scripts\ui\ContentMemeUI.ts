import { _decorator, Camera, Component, Node, Prefab, Vec3, instantiate, Sprite, UITransform } from 'cc';
import { ColorType } from '../enums/Enums';
import { ColorConfigs } from '../game/board/ColorConfigs';
const { ccclass, property } = _decorator;

@ccclass('ContentMemeUI')
export class ContentMemeUI extends Component {

    @property(Camera)
    public camera: Camera = null!;

    @property(Prefab)
    public memePrefab: Prefab = null!;

    private static _instance: ContentMemeUI | null = null;

    public static get Instance(): ContentMemeUI {
        if (!this._instance) {
            console.error("GameManager instance is not yet available.");
        }
        return this._instance!;
    }

    onLoad() {
        ContentMemeUI._instance = this;
    }

    public createMeme(pos: Vec3, color: ColorType) {
        const meme = instantiate(this.memePrefab);
        meme.setParent(this.node);

        const spr = ColorConfigs.Instance.getSpriteMemeFromColor(color);
        meme.getComponent(Sprite).spriteFrame = spr;

        // Thử cách khác: <PERSON><PERSON> dụng screenToWorld và convertToNodeSpaceAR
        const screenPos = new Vec3();
        this.camera.worldToScreen(pos, screenPos);

        console.log("World pos:", pos);
        console.log("Screen pos (normalized):", screenPos);

        // Lấy UITransform của canvas
        const canvasTransform = this.node.getComponent(UITransform);
        if (!canvasTransform) {
            console.error("Canvas UITransform not found");
            return;
        }

        // Thử nhiều cách khác nhau
        const canvasSize = canvasTransform.contentSize;

        // Cách 1: Manual calculation
        const uiX1 = screenPos.x * canvasSize.width - canvasSize.width / 2;
        const uiY1 = screenPos.y * canvasSize.height - canvasSize.height / 2;

        // Cách 2: Flip Y axis (vì có thể Y bị ngược)
        const uiX2 = screenPos.x * canvasSize.width - canvasSize.width / 2;
        const uiY2 = (1 - screenPos.y) * canvasSize.height - canvasSize.height / 2;

        // Cách 3: Sử dụng convertToNodeSpaceAR
        const realScreenPos = new Vec3(
            screenPos.x * canvasSize.width,
            screenPos.y * canvasSize.height,
            0
        );
        const localPos = new Vec3();
        canvasTransform.convertToNodeSpaceAR(realScreenPos, localPos);

        console.log("Method 1 (normal):", uiX1, uiY1);
        console.log("Method 2 (flip Y):", uiX2, uiY2);
        console.log("Method 3 (convertToNodeSpaceAR):", localPos);

        // Thử method 2 trước (flip Y)
        meme.setPosition(uiX2, uiY2, 0);
    }
}




