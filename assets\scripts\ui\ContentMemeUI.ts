import { _decorator, Camera, Component, Node, Prefab, Vec3, instantiate, Sprite, UITransform } from 'cc';
import { ColorType } from '../enums/Enums';
import { ColorConfigs } from '../game/board/ColorConfigs';
const { ccclass, property } = _decorator;

@ccclass('ContentMemeUI')
export class ContentMemeUI extends Component {

    @property(Camera)
    public camera: Camera = null!;

    @property(Prefab)
    public memePrefab: Prefab = null!;

    private static _instance: ContentMemeUI | null = null;

    public static get Instance(): ContentMemeUI {
        if (!this._instance) {
            console.error("GameManager instance is not yet available.");
        }
        return this._instance!;
    }

    onLoad() {
        ContentMemeUI._instance = this;
    }

    public createMeme(pos: Vec3, color: ColorType) {
        const meme = instantiate(this.memePrefab);
        meme.setParent(this.node);

        const spr = ColorConfigs.Instance.getSpriteMemeFromColor(color);
        meme.getComponent(Sprite).spriteFrame = spr;

        // <PERSON><PERSON>ển đổi từ world position 3D sang screen coordinates (normalized 0-1)
        const normalizedScreenPos = new Vec3();
        this.camera.worldToScreen(pos, normalizedScreenPos);

        // Debug log để kiểm tra
        console.log("World pos:", pos);
        console.log("Normalized screen pos:", normalizedScreenPos);

        // Lấy UITransform của canvas
        const canvasTransform = this.node.getComponent(UITransform);
        if (!canvasTransform) {
            console.error("Canvas UITransform not found");
            return;
        }

        const canvasSize = canvasTransform.contentSize;

        // Chuyển đổi từ normalized coordinates (0-1) sang pixel coordinates
        const pixelX = normalizedScreenPos.x * canvasSize.width;
        const pixelY = normalizedScreenPos.y * canvasSize.height;

        // Chuyển đổi từ pixel coordinates sang UI coordinates (center-based)
        const uiX = pixelX - canvasSize.width / 2;
        const uiY = pixelY - canvasSize.height / 2;

        console.log("Canvas size:", canvasSize);
        console.log("Pixel pos:", pixelX, pixelY);
        console.log("UI pos:", uiX, uiY);

        meme.setPosition(uiX, uiY, 0);
    }
}




