import { _decorator, Component, Label, Node, Sprite, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('MemeGame')
export class MemeGame extends Component {
    @property(Sprite) private icon: Sprite = null;
    @property(Label) private lblAmount: Label = null;

    public setupView(icon: SpriteFrame, amount: number) {
        this.icon.spriteFrame = icon;
        this.lblAmount.string = amount.toString();

        
    }
}


